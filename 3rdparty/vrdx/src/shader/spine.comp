#version 460 core

#extension GL_EXT_buffer_reference : require
#extension GL_KHR_shader_subgroup_basic: enable
#extension GL_KHR_shader_subgroup_arithmetic: enable
#extension GL_KHR_shader_subgroup_ballot: enable

const int RADIX = 256;
#define MAX_SUBGROUP_SIZE 128
#define WORKGROUP_SIZE 512
#define PARTITION_DIVISION 8
const int PARTITION_SIZE = PARTITION_DIVISION * WORKGROUP_SIZE;

// dispatch this shader (RADIX, 1, 1), so that gl_WorkGroupID.x is radix
layout (local_size_x = WORKGROUP_SIZE) in;

layout (buffer_reference, std430) readonly buffer ElementCount {
  uint elementCount;
};

layout (buffer_reference, std430) buffer GlobalHistogram {
  uint globalHistogram[];  // (4, R)
};

layout (buffer_reference, std430) buffer PartitionHistogram {
  uint partitionHistogram[];  // (P, R)
};

layout (push_constant) uniform PushConstant {
  int pass;
  restrict ElementCount elementCountReference;
  restrict GlobalHistogram globalHistogramReference;
  restrict PartitionHistogram partitionHistogramReference;
};

shared uint reduction;
// we only need array length equal to subgroup size = 32 or 64,
// but 128 shouldn't affect performance.
shared uint intermediate[MAX_SUBGROUP_SIZE];

void main() {
  uint threadIndex = gl_SubgroupInvocationID;  // 0..31 or 0..63
  uint subgroupIndex = gl_SubgroupID;  // 0..15 or 0..7
  uint index = subgroupIndex * gl_SubgroupSize + threadIndex;
  uint radix = gl_WorkGroupID.x;

  uint elementCount = elementCountReference.elementCount;

  uint partitionCount = (elementCount + PARTITION_SIZE - 1) / PARTITION_SIZE;

  if (index == 0) {
    reduction = 0;
  }
  barrier();

  for (uint i = 0; WORKGROUP_SIZE * i < partitionCount; ++i) {
    uint partitionIndex = WORKGROUP_SIZE * i + index;
    uint value = partitionIndex < partitionCount ? partitionHistogramReference.partitionHistogram[RADIX * partitionIndex + radix] : 0;
    uint excl = subgroupExclusiveAdd(value) + reduction;
    uint sum = subgroupAdd(value);

    if (subgroupElect()) {
      intermediate[subgroupIndex] = sum;
    }
    barrier();

    if (index < gl_NumSubgroups) {
      uint excl = subgroupExclusiveAdd(intermediate[index]);
      uint sum = subgroupAdd(intermediate[index]);
      intermediate[index] = excl;

      if (index == 0) {
        reduction += sum;
      }
    }
    barrier();

    if (partitionIndex < partitionCount) {
      excl += intermediate[subgroupIndex];
      partitionHistogramReference.partitionHistogram[RADIX * partitionIndex + radix] = excl;
    }
    barrier();
  }

  if (gl_WorkGroupID.x == 0) {
    // one workgroup is responsible for global histogram prefix sum
    if (index < RADIX) {
      uint value = globalHistogramReference.globalHistogram[RADIX * pass + index];
      uint excl = subgroupExclusiveAdd(value);
      uint sum = subgroupAdd(value);

      if (subgroupElect()) {
        intermediate[subgroupIndex] = sum;
      }
      barrier();

      if (index < RADIX / gl_SubgroupSize) {
        uint excl = subgroupExclusiveAdd(intermediate[index]);
        intermediate[index] = excl;
      }
      barrier();

      excl += intermediate[subgroupIndex];
      globalHistogramReference.globalHistogram[RADIX * pass + index] = excl;
    }
  }
}
